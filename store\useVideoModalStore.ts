import { create } from 'zustand';

interface VideoModalState {
    // 当前打开的视频ID
    videoId: string | null;
    // 弹窗是否打开
    isOpen: boolean;

    // Actions
    openModal: (videoId: string) => void;
    closeModal: () => void;
}

export const useVideoModalStore = create<VideoModalState>((set) => ({
    videoId: null,
    isOpen: false,

    openModal: (videoId: string) => {
        set({ videoId, isOpen: true });
    },

    closeModal: () => {
        set({ isOpen: false, videoId: null });
    }
})); 