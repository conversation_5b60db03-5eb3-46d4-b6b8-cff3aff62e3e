import { create } from 'zustand';

interface VideoModalState {
    // 当前打开的视频ID
    videoId: string | null;
    // 弹窗是否打开
    isOpen: boolean;

    // Actions
    openModal: (videoId: string) => void;
    closeModal: () => void;
}

export const useVideoModalStore = create<VideoModalState>((set) => ({
    videoId: null,
    isOpen: false,

    openModal: (videoId: string) => {
        set({ videoId, isOpen: true });
    },

    closeModal: () => {
        // 在关闭弹窗时，暂停所有视频
        if (typeof window !== 'undefined') {
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                video.pause();
                video.currentTime = 0;
            });
        }

        set({ isOpen: false });
    }
}));