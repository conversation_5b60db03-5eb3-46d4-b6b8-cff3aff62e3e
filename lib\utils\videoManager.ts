/**
 * 全局视频管理器 - 防止多个视频同时播放，避免音频泄漏
 */

class VideoManager {
    private static instance: VideoManager
    private currentPlayingVideo: HTMLVideoElement | null = null
    private allVideos: Set<HTMLVideoElement> = new Set()

    private constructor() {}

    static getInstance(): VideoManager {
        if (!VideoManager.instance) {
            VideoManager.instance = new VideoManager()
        }
        return VideoManager.instance
    }

    /**
     * 注册视频元素
     */
    registerVideo(video: HTMLVideoElement): void {
        this.allVideos.add(video)
        
        // 添加事件监听器
        const handlePlay = () => {
            this.setCurrentPlayingVideo(video)
        }
        
        const handlePause = () => {
            if (this.currentPlayingVideo === video) {
                this.currentPlayingVideo = null
            }
        }

        const handleEnded = () => {
            if (this.currentPlayingVideo === video) {
                this.currentPlayingVideo = null
            }
        }

        video.addEventListener('play', handlePlay)
        video.addEventListener('pause', handlePause)
        video.addEventListener('ended', handleEnded)

        // 存储清理函数
        ;(video as any)._videoManagerCleanup = () => {
            video.removeEventListener('play', handlePlay)
            video.removeEventListener('pause', handlePause)
            video.removeEventListener('ended', handleEnded)
            this.unregisterVideo(video)
        }
    }

    /**
     * 注销视频元素
     */
    unregisterVideo(video: HTMLVideoElement): void {
        this.allVideos.delete(video)
        if (this.currentPlayingVideo === video) {
            this.currentPlayingVideo = null
        }
        
        // 执行清理
        if ((video as any)._videoManagerCleanup) {
            (video as any)._videoManagerCleanup()
            delete (video as any)._videoManagerCleanup
        }
    }

    /**
     * 设置当前播放的视频，暂停其他所有视频
     */
    setCurrentPlayingVideo(video: HTMLVideoElement): void {
        // 暂停所有其他视频
        this.allVideos.forEach(v => {
            if (v !== video && !v.paused) {
                v.pause()
                v.currentTime = 0 // 重置播放位置
            }
        })
        
        this.currentPlayingVideo = video
    }

    /**
     * 暂停所有视频
     */
    pauseAllVideos(): void {
        this.allVideos.forEach(video => {
            if (!video.paused) {
                video.pause()
                video.currentTime = 0
            }
        })
        this.currentPlayingVideo = null
    }

    /**
     * 清理所有视频资源
     */
    cleanup(): void {
        this.allVideos.forEach(video => {
            video.pause()
            video.currentTime = 0
            video.src = ''
            video.load()
        })
        this.allVideos.clear()
        this.currentPlayingVideo = null
    }

    /**
     * 获取当前播放的视频
     */
    getCurrentPlayingVideo(): HTMLVideoElement | null {
        return this.currentPlayingVideo
    }

    /**
     * 获取所有注册的视频数量
     */
    getVideoCount(): number {
        return this.allVideos.size
    }
}

// 导出单例实例
export const videoManager = VideoManager.getInstance()

// 页面卸载时清理所有视频
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        videoManager.cleanup()
    })
    
    // 页面隐藏时暂停所有视频
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            videoManager.pauseAllVideos()
        }
    })
}
