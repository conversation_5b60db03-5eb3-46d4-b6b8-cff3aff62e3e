"use client"

import { useEffect, useRef, useState } from "react"
import { PostItemDto } from "@/types/posts"
import { Pause, Play, Volume2, VolumeX, Setting<PERSON>, Maximize, Ski<PERSON>For<PERSON>, SkipBack } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getVideoStyle } from "../utils/videoUtils"
import { PlayButton } from "./PlayButton"
import { usePostStats } from "@/hooks/use-post-stats"

interface VideoPlayerProps {
    post: PostItemDto
    isModal?: boolean
}

// 格式化时间为 MM:SS 格式
const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// 自定义进度条组件
const ProgressBar = ({
    value,
    onChange,
    className,
}: {
    value: number;
    onChange: (value: number) => void;
    className?: string;
}) => {
    return (
        <div
            className={`relative w-full h-1.5 bg-slate-300/30 dark:bg-white/20 rounded-full cursor-pointer group ${className || ""}`}
            onClick={(e) => {
                const rect = e.currentTarget.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const percentage = (x / rect.width) * 100;
                onChange(Math.min(Math.max(percentage, 0), 100));
            }}
        >
            <div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full group-hover:from-blue-400 group-hover:to-indigo-500"
                style={{ width: `${value}%` }}
            />
            <div
                className="absolute top-0 -translate-y-50% h-3 w-3 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                style={{ left: `${value}%`, transform: `translateX(-50%) translateY(-50%)` }}
            />
        </div>
    );
};

export function VideoPlayer({ post, isModal = false }: VideoPlayerProps) {
    const videoRef = useRef<HTMLVideoElement>(null)
    const [isPlaying, setIsPlaying] = useState(false)
    const [isMuted, setIsMuted] = useState(true)
    const [isLoading, setIsLoading] = useState(true)
    const [progress, setProgress] = useState(0)
    const [currentTime, setCurrentTime] = useState(0)
    const [duration, setDuration] = useState(0)
    const [volume, setVolume] = useState(1)
    const [showControls, setShowControls] = useState(false)
    const containerRef = useRef<HTMLDivElement>(null)
    const { recordClick, isStatsEnabled } = usePostStats() // 使用统计Hook

    // 自动播放视频
    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.play().then(() => {
                setIsPlaying(true)
            }).catch(e => {
                console.log("Auto play failed, may require user interaction:", e)
                setIsPlaying(false)
            })
        }

        const handleLoadedData = () => {
            setIsLoading(false)
            if (videoRef.current) {
                setDuration(videoRef.current.duration)
            }
        }

        const video = videoRef.current
        video?.addEventListener('loadeddata', handleLoadedData)

        return () => {
            video?.removeEventListener('loadeddata', handleLoadedData)
            // 组件卸载时强制停止视频播放
            if (video) {
                video.pause()
                video.currentTime = 0
                // 移除 src 和 load()，直接暂停就足够了
                // video.src = ''
                // video.load()
            }
        }
    }, [])

    // 处理视频时间更新
    useEffect(() => {
        const handleTimeUpdate = () => {
            if (videoRef.current) {
                const currentTime = videoRef.current.currentTime
                const duration = videoRef.current.duration
                const progress = (currentTime / duration) * 100

                setCurrentTime(currentTime)
                setProgress(isFinite(progress) ? progress : 0)
            }
        }

        const video = videoRef.current
        video?.addEventListener('timeupdate', handleTimeUpdate)

        return () => {
            video?.removeEventListener('timeupdate', handleTimeUpdate)
        }
    }, [])

    // 播放/暂停切换
    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play()

                // 记录点击量统计 - 只在开始播放时记录，且仅在统计功能启用时
                if (post.id && !isPlaying && isStatsEnabled) {
                    recordClick(post.id)
                }
            }
            setIsPlaying(!isPlaying)
        }
    }

    // 静音切换
    const toggleMute = () => {
        if (videoRef.current) {
            videoRef.current.muted = !isMuted
            setIsMuted(!isMuted)
        }
    }

    // 音量调节
    const handleVolumeChange = (value: number) => {
        if (videoRef.current) {
            const newVolume = value / 100
            videoRef.current.volume = newVolume
            setVolume(newVolume)
            if (newVolume === 0) {
                videoRef.current.muted = true
                setIsMuted(true)
            } else if (isMuted) {
                videoRef.current.muted = false
                setIsMuted(false)
            }
        }
    }

    // 进度条拖动
    const handleSeek = (value: number) => {
        if (videoRef.current) {
            const newTime = (value / 100) * duration
            videoRef.current.currentTime = newTime
            setCurrentTime(newTime)
            setProgress(value)
        }
    }

    // 快进
    const skipForward = () => {
        if (videoRef.current) {
            videoRef.current.currentTime = Math.min(videoRef.current.currentTime + 10, duration)
        }
    }

    // 快退
    const skipBackward = () => {
        if (videoRef.current) {
            videoRef.current.currentTime = Math.max(videoRef.current.currentTime - 10, 0)
        }
    }

    // 全屏切换
    const toggleFullscreen = () => {
        if (!document.fullscreenElement) {
            containerRef.current?.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable full-screen mode: ${err.message}`)
            })
        } else {
            document.exitFullscreen()
        }
    }

    return (
        <div
            ref={containerRef}
            className="relative w-full h-full bg-black group cursor-pointer"
            onMouseEnter={() => setShowControls(true)}
            onMouseLeave={() => setShowControls(false)}
        >
            {/* 视频元素 */}
            <video
                ref={videoRef}
                src={post.video_url}
                className='w-full h-full object-contain'
                playsInline
                loop
                autoPlay
                muted={isMuted}
                onClick={(e) => {
                    e.stopPropagation()
                    togglePlay()
                }}
                // 添加预加载和SEO属性
                preload="metadata"
                poster={post.videos?.cover_img || ''}
                // 为SEO添加属性
                controls={false}
                controlsList="nodownload"
                // 添加a11y属性
                aria-label={post.title || `Video by ${post.username}`}
            />

            {/* 加载指示器 */}
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
                    <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
            )}

            {/* 视频控制器 */}
            <AnimatePresence>
                {(showControls || !isPlaying) && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="fixed bottom-0 inset-x-0 left-0 flex flex-col justify-end p-4 z-20 w-full h-full"
                        onClick={togglePlay}
                    >
                        {/* 控制面板背景 - 渐变效果 */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80 pointer-events-none"></div>

                        {/* 中央播放/暂停按钮 */}
                        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <PlayButton isPlaying={isPlaying} showMotion={true} />
                        </div>

                        {/* 底部控制栏 */}
                        <div className="relative z-10 space-y-2">
                            {/* 进度条 */}
                            <ProgressBar
                                value={progress}
                                onChange={handleSeek}
                                className="mb-2"
                            />

                            {/* 控制按钮和时间显示 */}
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    {/* 播放/暂停按钮 */}
                                    <button
                                        className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                        onClick={togglePlay}
                                    >
                                        {isPlaying ? (
                                            <Pause className="w-5 h-5 text-white" />
                                        ) : (
                                            <Play className="w-5 h-5 text-white" />
                                        )}
                                    </button>

                                    {/* 快退按钮 */}
                                    <button
                                        className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                        onClick={skipBackward}
                                    >
                                        <SkipBack className="w-5 h-5 text-white" />
                                    </button>

                                    {/* 快进按钮 */}
                                    <button
                                        className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                        onClick={skipForward}
                                    >
                                        <SkipForward className="w-5 h-5 text-white" />
                                    </button>

                                    {/* 时间显示 */}
                                    <div className="text-white text-sm">
                                        {formatTime(currentTime)} / {formatTime(duration)}
                                    </div>
                                </div>

                                <div className="flex items-center space-x-3">
                                    {/* 音量控制 */}
                                    <div className="relative group">
                                        <button
                                            className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                            onClick={toggleMute}
                                        >
                                            {isMuted ? (
                                                <VolumeX className="w-5 h-5 text-white" />
                                            ) : (
                                                <Volume2 className="w-5 h-5 text-white" />
                                            )}
                                        </button>
                                        <div className="absolute left-0  mb-2 w-24 h-1.5 bg-slate-300/30 dark:bg-white/20 rounded-full hidden group-hover:block">
                                            <div
                                                className="absolute top-0 left-0 h-full bg-blue-500 rounded-full"
                                                style={{ width: `${volume * 100}%` }}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    const rect = e.currentTarget.parentElement?.getBoundingClientRect();
                                                    if (rect) {
                                                        const x = e.clientX - rect.left;
                                                        const percentage = (x / rect.width) * 100;
                                                        handleVolumeChange(Math.min(Math.max(percentage, 0), 100));
                                                    }
                                                }}
                                            />
                                        </div>
                                    </div>
                                    {/* 设置按钮 */}
                                    <button className="p-1 rounded-full hover:bg-white/10 transition-colors">
                                        <Settings className="w-5 h-5 text-white" />
                                    </button>

                                    {/* 全屏按钮 */}
                                    <button
                                        className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                        onClick={toggleFullscreen}
                                    >
                                        <Maximize className="w-5 h-5 text-white" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    )
}

// 这是一个简化版本的VideoPlayer组件，供服务端渲染使用
export function ServerVideoPlayer({ post, isModal = false }: VideoPlayerProps) {
    // 添加播放状态
    const [isPlaying, setIsPlaying] = useState(true)
    // 添加视频引用
    const videoRef = useRef<HTMLVideoElement>(null)
    // 使用统计Hook
    const { recordClick, isStatsEnabled } = usePostStats()

    // 组件卸载时清理视频
    useEffect(() => {
        return () => {
            const video = videoRef.current
            if (video) {
                video.pause()
                video.currentTime = 0
                // 组件销毁时只需要暂停，不需要 load()
            }
        }
    }, [])

    // 播放/暂停切换
    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play().catch(e => {
                    console.log("播放失败，可能需要用户交互:", e)
                })

                // 记录点击量统计 - 只在开始播放时记录，且仅在统计功能启用时
                if (post.id && !isPlaying && isStatsEnabled) {
                    recordClick(post.id)
                }
            }
            setIsPlaying(!isPlaying)
        }
    }

    return (
        <figure className="relative w-full h-full bg-black">
            {/* 视频元素 - 添加ref引用 */}
            <video
                ref={videoRef}
                src={post.video_url}
                className='w-full h-full object-contain'
                autoPlay
                muted
                playsInline
                loop
                poster={post.videos?.cover_img}
                controls={false}
                controlsList="nodownload"
                aria-label={post.title || `Video by ${post.username}`}
            />

            {/* 播放按钮覆盖层 */}
            <div
                className="absolute inset-0 flex items-center justify-center"
                onClick={togglePlay}
                aria-label={isPlaying ? "Pause video" : "Play video"}
            >
                <PlayButton isPlaying={isPlaying} showMotion={true} />
            </div>
        </figure>
    )
}