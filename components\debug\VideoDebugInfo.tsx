"use client"

import { useEffect, useState } from "react"
import { videoManager } from "@/lib/utils/videoManager"

/**
 * 视频调试信息组件 - 用于监控视频播放状态和内存使用情况
 * 仅在开发环境中显示
 */
export function VideoDebugInfo() {
    const [videoCount, setVideoCount] = useState(0)
    const [currentPlaying, setCurrentPlaying] = useState<string | null>(null)
    const [memoryInfo, setMemoryInfo] = useState<any>(null)

    useEffect(() => {
        // 只在开发环境中显示
        if (process.env.NODE_ENV !== 'development') return

        const updateDebugInfo = () => {
            setVideoCount(videoManager.getVideoCount())
            
            const currentVideo = videoManager.getCurrentPlayingVideo()
            setCurrentPlaying(currentVideo ? currentVideo.src : null)

            // 获取内存信息（如果可用）
            if ('memory' in performance) {
                setMemoryInfo((performance as any).memory)
            }
        }

        // 初始更新
        updateDebugInfo()

        // 定期更新
        const interval = setInterval(updateDebugInfo, 1000)

        return () => clearInterval(interval)
    }, [])

    // 只在开发环境中渲染
    if (process.env.NODE_ENV !== 'development') {
        return null
    }

    return (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-xs">
            <div className="space-y-1">
                <div>📹 Videos: {videoCount}</div>
                <div>▶️ Playing: {currentPlaying ? 'Yes' : 'None'}</div>
                {memoryInfo && (
                    <>
                        <div>💾 Used: {Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024)}MB</div>
                        <div>📊 Total: {Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024)}MB</div>
                        <div>🔄 Limit: {Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024)}MB</div>
                    </>
                )}
                <button 
                    onClick={() => videoManager.pauseAllVideos()}
                    className="mt-2 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs"
                >
                    Pause All
                </button>
                <button 
                    onClick={() => videoManager.cleanup()}
                    className="mt-1 px-2 py-1 bg-orange-600 hover:bg-orange-700 rounded text-xs"
                >
                    Cleanup
                </button>
            </div>
        </div>
    )
}
