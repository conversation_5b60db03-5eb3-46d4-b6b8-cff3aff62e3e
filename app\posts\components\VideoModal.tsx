"use client"

import { useEffect, useRef } from "react"
import { VideoDetailLayout } from "./VideoDetailLayout"
import { VideoAreaSkeleton, InfoPanelSkeleton } from "./VideoDetailSkeleton"
import { motion, AnimatePresence } from "framer-motion"
import { useVideoDetailStore } from "@/store/useVideoDetailStore"
import { useAuth } from "@/contexts/auth-context";

interface VideoModalProps {
    videoId: string | null
    onClose: () => void
    isOpen: boolean
}

export function VideoModal({ videoId, onClose, isOpen }: VideoModalProps) {
    const modalRef = useRef<HTMLDivElement>(null)
    const { isAuthenticated } = useAuth();

    // 使用zustand store
    const {
        post,
        postLoading,
        error,
        comments,
        loadingComments,
        commentContent,
        submittingComment,
        checkingFavorite,
        fetchPost,
        fetchComments,
        checkFavorite,
        setCommentContent,
        submitComment,
        resetState
    } = useVideoDetailStore()

    // 组件加载时获取数据
    useEffect(() => {
        // 防止无效ID
        if (!videoId || !isOpen) return

        // 分别加载各个数据（帖子、评论和收藏状态）
        fetchPost(videoId)
        fetchComments(videoId)
        isAuthenticated && checkFavorite(videoId)

        // 更新浏览器历史记录，但不导航
        const currentUrl = window.location.href
        window.history.pushState({ videoId, previousUrl: currentUrl }, "", `/posts/${videoId}`)

        // 添加历史记录弹出事件监听器
        const handlePopState = (event: PopStateEvent) => {
            onClose()
        }
        window.addEventListener("popstate", handlePopState)

        // 禁止背景滚动
        document.body.style.overflow = 'hidden'

        // 组件卸载时重置状态和移除事件监听器
        return () => {
            window.removeEventListener("popstate", handlePopState)
            // 恢复背景滚动
            document.body.style.overflow = ''

            // 停止所有视频播放
            const videos = document.querySelectorAll('video')
            videos.forEach(video => {
                video.pause()
                video.currentTime = 0
            })

            if (isOpen) {
                resetState()
            }
        }
    }, [videoId, isOpen, fetchPost, fetchComments, checkFavorite, resetState, onClose])

    // 点击外部关闭弹窗
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // 检查事件目标是否来自Dialog（登录弹窗）
            // 如果事件目标或其父元素有z-200类，说明是Dialog
            const isDialogClick = (event.target as Element)?.closest('.z-200') !== null;

            // 只有当不是Dialog的点击且点击在modal外部时才关闭
            if (modalRef.current &&
                !modalRef.current.contains(event.target as Node) &&
                !isDialogClick) {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside)
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [isOpen, onClose])

    // 按ESC关闭弹窗
    useEffect(() => {
        const handleEscKey = (event: KeyboardEvent) => {
            if (event.key === "Escape") {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("keydown", handleEscKey)
        }

        return () => {
            document.removeEventListener("keydown", handleEscKey)
        }
    }, [isOpen, onClose])

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    ref={modalRef}
                    className="fixed inset-0 z-[100] bg-black/90 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <motion.div
                        className="relative w-full h-full overflow-hidden"
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.95, opacity: 0 }}
                        transition={{ duration: 0.15 }}
                    >
                        {/* 错误状态 */}
                        {error && (
                            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-950 flex items-center justify-center p-4">
                                <motion.div
                                    className="max-w-md w-full bg-white dark:bg-slate-800 rounded-2xl shadow-lg overflow-hidden border border-slate-200/50 dark:border-slate-700/50"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="p-6 border-b border-slate-200/50 dark:border-slate-700/50">
                                        <h2 className="text-xl font-semibold text-red-500 dark:text-red-400 mb-2">
                                            Cannot load video
                                        </h2>
                                        <p className="text-slate-600 dark:text-slate-300">
                                            {error || "The requested video cannot be found or is currently unavailable."}
                                        </p>
                                    </div>
                                    <div className="p-4 bg-slate-50 dark:bg-slate-800/50 flex justify-end">
                                        <button
                                            onClick={() => onClose()}
                                            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors shadow-sm"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </motion.div>
                            </div>
                        )}

                        {/* 分块加载视图 - 当没有错误时显示 */}
                        {!error && (
                            <div className="w-full h-full">
                                {/* 使用分块加载骨架屏 */}
                                {postLoading && !post && (
                                    <div className="h-full w-full px-2 sm:px-4 py-2 sm:py-4 relative z-10">
                                        <div className="flex flex-col lg:flex-row gap-2 sm:gap-4 h-full">
                                            <VideoAreaSkeleton />
                                            <InfoPanelSkeleton />
                                        </div>
                                    </div>
                                )}

                                {/* 视频内容 - 只要帖子数据已加载就显示内容，评论和收藏则根据各自加载状态决定显示 */}
                                {post && <VideoDetailLayout
                                    post={post}
                                    comments={comments}
                                    loadingComments={loadingComments}
                                    commentContent={commentContent}
                                    submittingComment={submittingComment}
                                    checkingFavorite={checkingFavorite}
                                    setCommentContent={setCommentContent}
                                    submitComment={submitComment}
                                    isModal={true}
                                />
                                }
                            </div>
                        )}
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    )
} 