import { useState, useRef, useEffect } from "react"
import type { PostItem } from "@/types/posts"
import { useVideoModalStore } from "@/store/useVideoModalStore"
import { useRouter } from "next/navigation"
import { CardShareButton } from "./card-share-button"
import { CardInfoPanel } from "./card-info-panel"
import { CardRemixButton } from "./card-remix-button"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"
import { safePlayAttempt } from "@/lib/utils"
import { usePostStats } from "@/hooks/use-post-stats"
import { Play } from "lucide-react"

interface ContentCardProps {
  item: PostItem
}

export function ContentCard({ item }: ContentCardProps) {
  const router = useRouter();
  const [isHovering, setIsHovering] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [isImageLoaded, setIsImageLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const [videoDuration, setVideoDuration] = useState(item.inputParams?.duration || "")
  const [userInteracted, setUserInteracted] = useState(false) // 用户是否手动控制过视频
  const [shouldLoadVideo, setShouldLoadVideo] = useState(false) // 控制视频加载时机
  const [playAttemptFailed, setPlayAttemptFailed] = useState(false) // 追踪播放尝试是否失败
  const videoRef = useRef<HTMLVideoElement>(null)
  const cardRef = useRef<HTMLDivElement>(null)
  const { recordView, recordClick } = usePostStats() // 使用统计Hook

  // 使用视频弹窗状态
  const { openModal } = useVideoModalStore()

  // 在服务器端和客户端使用相同的宽高比值
  const getAspectRatioClass = (ratio: "16:9" | "9:16" | "1:1") => {
    if (ratio === "16:9") return "aspect-video"
    if (ratio === "9:16") return "aspect-9/16"
    return "aspect-square" // 默认为正方形(1:1)
  }

  // 使用 Intersection Observer 检测元素是否在视口中 - 优化版本
  useEffect(() => {
    if (!cardRef.current) return

    // 创建一个观察器实例
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        const wasInView = isInView
        const isNowInView = entry.isIntersecting

        // 更新视图状态
        setIsInView(isNowInView)

        // 当卡片进入视图，且没有加载过视频时，控制延迟加载视频
        if (!wasInView && isNowInView && !shouldLoadVideo) {
          // 使用requestIdleCallback或requestAnimationFrame延迟加载视频
          // 这样可以优先处理更重要的渲染任务
          if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
            // @ts-ignore - TypeScript可能不认识requestIdleCallback
            window.requestIdleCallback(() => {
              setShouldLoadVideo(true)

              // 当卡片首次进入视图时，记录浏览量
              if (item.id) {
                recordView(item.id)
              }
            }, { timeout: 1000 }) // 设置最大延迟为1秒
          } else {
            // 回退到requestAnimationFrame
            requestAnimationFrame(() => {
              setShouldLoadVideo(true)

              // 当卡片首次进入视图时，记录浏览量
              if (item.id) {
                recordView(item.id)
              }
            })
          }
        }

        // 当视频从不可见变为可见时，重置用户交互状态
        if (!wasInView && isNowInView) {
          setUserInteracted(false)
        }

        // 当视频离开视图时，如果正在播放，则暂停视频以节省资源
        if (wasInView && !isNowInView && videoRef.current && !userInteracted) {
          videoRef.current.pause()
        }
      },
      {
        root: null, // 使用视口作为根
        rootMargin: '200px', // 提前200px开始加载，提供更好的预加载体验
        threshold: 0.1, // 当10%的元素可见时触发
      }
    )

    // 开始观察
    observer.observe(cardRef.current)

    // 清理函数
    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current)
      }
    }
  }, [isInView, shouldLoadVideo, userInteracted, recordView, item.id])

  // 当应该加载视频时，预加载视频 - 优化版本
  useEffect(() => {
    if (shouldLoadVideo && videoRef.current && !isVideoLoaded) {
      // 设置视频加载优先级
      if ('fetchPriority' in videoRef.current) {
        // @ts-ignore - fetchPriority 是较新的属性，TypeScript可能不认识
        videoRef.current.fetchPriority = 'low'
      }

      // 设置预加载策略
      videoRef.current.preload = 'metadata'

      // 使用requestIdleCallback在浏览器空闲时加载视频
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        // @ts-ignore
        window.requestIdleCallback(() => {
          if (videoRef.current) {
            // 从data属性获取视频URL
            const videoUrl = videoRef.current.dataset.videoUrl
            if (videoUrl && !videoRef.current.src) {
              videoRef.current.src = videoUrl
              videoRef.current.load()
            }
          }
        }, { timeout: 2000 })
      } else {
        // 回退到setTimeout，延迟加载以优先处理其他任务
        setTimeout(() => {
          if (videoRef.current) {
            // 从data属性获取视频URL
            const videoUrl = videoRef.current.dataset.videoUrl
            if (videoUrl && !videoRef.current.src) {
              videoRef.current.src = videoUrl
              videoRef.current.load()
            }
          }
        }, 100)
      }
    }
  }, [shouldLoadVideo, isVideoLoaded])

  // 检测浏览器是否可能处于省电模式
  const isPowerSaveMode = () => {
    // 1. 检查电池状态API (如果可用)
    if ('getBattery' in navigator) {
      // @ts-ignore - getBattery 可能不在所有TypeScript定义中都有
      navigator.getBattery().then((battery) => {
        if (battery.dischargingTime !== Infinity && battery.level < 0.15) {
          return true; // 电池电量低时可能启用省电模式
        }
      }).catch(() => {});
    }

    // 2. 检测是否是移动设备 (移动设备更可能启用省电模式)
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      typeof navigator !== 'undefined' ? navigator.userAgent : ''
    );

    return isMobile;
  };

  // 视频播放控制 - 优化版本
  useEffect(() => {
    // 只有当用户没有手动干预时，才应用自动播放/暂停逻辑
    if (userInteracted || !videoRef.current || !isVideoLoaded) return;

    // 创建一个防抖函数来处理播放尝试，避免频繁尝试播放导致性能问题
    const debouncedPlayAttempt = debounce(() => {
      if (videoRef.current && isInView && !playAttemptFailed) {
        // 尝试自动播放，使用安全播放方法
        safePlayAttempt(videoRef.current, setPlayAttemptFailed);
      }
    }, 150);

    if (isInView) {
      // 当元素在视口内时，尝试播放
      debouncedPlayAttempt();
    } else if (videoRef.current) {
      // 当元素离开视口时，暂停视频
      videoRef.current.pause();
    }

    // 清理函数
    return () => {
      debouncedPlayAttempt.cancel();
    };
  }, [isInView, isVideoLoaded, userInteracted, playAttemptFailed]);

  // 鼠标悬停时的额外交互 - 优化版本
  useEffect(() => {
    // 只在必要条件满足时执行
    if (userInteracted || !isHovering || !videoRef.current || isPlaying || !isVideoLoaded) return;

    // 检查是否可以播放
    const canPlay = !isPowerSaveMode() && !playAttemptFailed;

    if (canPlay) {
      // 使用requestAnimationFrame确保在下一帧尝试播放，避免阻塞渲染
      requestAnimationFrame(() => {
        if (videoRef.current && isHovering) {
          safePlayAttempt(videoRef.current, setPlayAttemptFailed);
        }
      });
    }
  }, [isHovering, isVideoLoaded, isPlaying, userInteracted, playAttemptFailed]);

  // 防抖函数
  function debounce<T extends (...args: any[]) => any>(func: T, wait: number) {
    let timeout: NodeJS.Timeout | null = null;

    const debounced = (...args: Parameters<T>) => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        func(...args);
      }, wait);
    };

    // 添加取消方法
    debounced.cancel = () => {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
    };

    return debounced;
  }

  const handleMouseEnter = () => {
    setIsHovering(true)
  }

  const handleMouseLeave = () => {
    setIsHovering(false)
  }

  // 修复播放/暂停按钮状态与视频实际状态不同步的问题
  useEffect(() => {
    const videoElement = videoRef.current
    if (videoElement) {
      const handlePlay = () => {
        setIsPlaying(true)
      }
      const handlePause = () => setIsPlaying(false)

      videoElement.addEventListener('play', handlePlay)
      videoElement.addEventListener('pause', handlePause)

      return () => {
        videoElement.removeEventListener('play', handlePlay)
        videoElement.removeEventListener('pause', handlePause)
      }
    }
  }, [])

  const handleVideoLoaded = () => {
    setIsVideoLoaded(true)

    // 获取实际视频时长
    if (videoRef.current) {
      const minutes = Math.floor(videoRef.current.duration / 60)
      const seconds = Math.floor(videoRef.current.duration % 60)
      setVideoDuration(`${minutes}:${seconds.toString().padStart(2, '0')}`)
    }

    // 如果在视口内且用户未手动干预，自动开始播放
    if (isInView && !userInteracted && videoRef.current) {
      // 使用安全播放方法
      safePlayAttempt(videoRef.current, setPlayAttemptFailed)
    }
  }

  // 处理卡片点击，打开弹窗并记录点击量
  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault()

    // 记录点击量统计
    if (item.id) {
      recordClick(item.id)
    }

    // 打开弹窗
    openModal(item.id)
  }

  // 处理Remix按钮点击事件 - 跳转到创建页面
  const handleRemixClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation() // 阻止事件冒泡，不触发卡片点击

    if (!item.taskId) return;

    // 导航到创建页面并传递task_id
    router.push(`/create?remix=${item.taskId}`)
  }

  // 处理分享按钮点击事件
  const handleShareClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation() // 阻止事件冒泡，不触发卡片点击

    // 具体逻辑在CardShareButton组件内部处理
  }

  // 截断标题，保持简洁
  const truncateTitle = (title: string, maxLength: number = 60) => {
    if (!title) return '';
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  }

  return (
    <motion.div
      ref={cardRef}
      className="block w-full bg-card rounded-lg text-card-foreground overflow-hidden group cursor-pointer"
      whileHover={{ scale: 1.02, y: -2 }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleCardClick}
    >
      <div className={`relative ${getAspectRatioClass(item.inputParams?.ratio || "9:16")} bg-muted`}>
        {/* 缩略图 - 优化加载策略 */}
        {!isVideoLoaded && item.coverImg && (
          <Image
            src={item.coverImg}
            alt={item.title || "视频内容"}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            priority={isInView} // 只为视口内的图片设置优先级
            className="object-cover transition-transform duration-700 group-hover:scale-105"
            onLoad={() => setIsImageLoaded(true)}
            loading={isInView ? "eager" : "lazy"} // 根据可见性设置加载策略
            fetchPriority={isInView ? "high" : "auto"} // 使用fetchPriority属性
            decoding="async" // 使用异步解码
            style={{
              contentVisibility: isInView ? "visible" : "auto" // 使用contentVisibility优化
            }}
          />
        )}

        {/* 懒加载视频元素 - 优化版本 */}
        {shouldLoadVideo && (
          <video
            ref={videoRef}
            // 使用data属性存储视频URL，延迟加载视频
            data-video-url={item.videoUrl}
            preload="metadata"
            muted
            playsInline
            loop
            onLoadedData={handleVideoLoaded}
            className={`absolute inset-0 w-full h-full object-cover ${isVideoLoaded ? 'opacity-100' : 'opacity-0'
              } transition-all duration-700 group-hover:scale-105`}
            poster={item.coverImg || undefined}
            // 增加关键属性以改善自动播放行为
            data-silent="true"
            disablePictureInPicture
            // 添加额外属性以提高性能
            controlsList="nodownload nofullscreen noremoteplayback"
          />
        )}

        {/* 骨架屏 - 在图片或视频加载前显示，使用渐变背景提高视觉体验 */}
        {!isImageLoaded && !isVideoLoaded && (
          <div className="absolute inset-0 animate-pulse">
            <div className="w-full h-full bg-gradient-to-b from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-800 dark:to-gray-700"></div>
          </div>
        )}

        {/* 始终存在的标题遮罩层 - 简化为水平渐变，位于底部 */}
        <div
          className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent h-24 z-10"
        ></div>

        {/* 播放指示器 - 仅在悬停时显示 */}
        {isHovering && !isPlaying && (
          <AnimatePresence>
            <motion.div
              className="absolute inset-0 flex items-center justify-center z-20"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-3 bg-black/50 rounded-full backdrop-blur-sm">
                <Play className="h-8 w-8 text-white" fill="white" />
              </div>
            </motion.div>
          </AnimatePresence>
        )}

        {/* 顶部时长指示器 */}
        <div className="absolute top-2 right-2 z-20">
          <motion.span
            className="bg-black/70 text-white text-xs px-2 py-1 rounded-full shadow-sm backdrop-blur-[2px]"
            animate={{
              opacity: isHovering ? 1 : 0.7,
              scale: isHovering ? 1.05 : 1
            }}
            transition={{ duration: 0.3 }}
          >
            {videoDuration}
          </motion.span>
        </div>

        {/* 底部展开区域 - 仅在悬停时显示 */}
        {isHovering && (
          <AnimatePresence>
            <motion.div
              className="absolute bottom-0 left-0 right-0 p-3 z-30 pt-12 bg-gradient-to-t from-black/90 via-black/80 to-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                opacity: { duration: 0.3 }
              }}
            >
              {/* 参数信息面板 - 简化版本 */}
              {item.inputParams && (
                <CardInfoPanel
                  inputParams={item.inputParams}
                  isExpanded={true}
                  showPrompt={!item.title.toLowerCase().includes(item.inputParams.prompt.toLowerCase().substring(0, 15))}
                />
              )}

              {/* 底部按钮区域 */}
              <motion.div
                className="flex items-center gap-3 mt-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                {/* 分享按钮 */}
                <div className="w-28">
                  <CardShareButton
                    isHovering={true}
                    isExpanded={true}
                    postId={item.id}
                    onClick={handleShareClick}
                  />
                </div>

                {/* Remix 按钮 */}
                <div className="flex-1">
                  <CardRemixButton onClick={handleRemixClick} taskId={item.taskId} />
                </div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    </motion.div>
  )
}

