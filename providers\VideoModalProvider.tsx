"use client"

import { ReactNode } from "react"
import { VideoModal } from "../app/posts/components/VideoModal"
import { useVideoModalStore } from "@/store/useVideoModalStore"

interface VideoModalProviderProps {
    children: ReactNode
}

export function VideoModalProvider({ children }: VideoModalProviderProps) {
    const { videoId, isOpen, closeModal } = useVideoModalStore()

    return (
        <>
            {children}
            {/* 只有在 isOpen 为 true 且 videoId 存在时才渲染 VideoModal，确保组件被真正销毁 */}
            {isOpen && videoId && (
                <VideoModal
                    videoId={videoId}
                    isOpen={isOpen}
                    onClose={closeModal}
                />
            )}
        </>
    )
} 