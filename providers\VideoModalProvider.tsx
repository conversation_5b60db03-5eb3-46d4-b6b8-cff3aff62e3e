"use client"

import { ReactNode } from "react"
import { VideoModal } from "../app/posts/components/VideoModal"
import { useVideoModalStore } from "@/store/useVideoModalStore"

interface VideoModalProviderProps {
    children: ReactNode
}

export function VideoModalProvider({ children }: VideoModalProviderProps) {
    const { videoId, isOpen, closeModal } = useVideoModalStore()

    return (
        <>
            {children}
            {/* 关键修复：只有在 isOpen 为 true 且 videoId 存在时才渲染 VideoModal */}
            {/* 这确保了当弹窗关闭时，VideoModal 组件被完全销毁，而不是仅仅隐藏 */}
            {isOpen && videoId && (
                <VideoModal
                    videoId={videoId}
                    isOpen={isOpen}
                    onClose={closeModal}
                />
            )}
        </>
    )
} 