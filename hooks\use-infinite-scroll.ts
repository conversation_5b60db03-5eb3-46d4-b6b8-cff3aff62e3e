"use client"

import { useState, useEffect, useCallback, useRef } from "react"

interface UseInfiniteScrollOptions {
    rootMargin?: string // Intersection Observer的rootMargin
    isLoading?: boolean // 是否正在加载
    hasMore?: boolean // 是否还有更多数据
    retryOnError?: boolean // 是否在错误时自动重试
    retryDelay?: number // 重试延迟(ms)
}

export function useInfiniteScroll(onLoadMore: () => Promise<void>, options: UseInfiniteScrollOptions = {}) {
    const {
        rootMargin = "300px 0px",
        isLoading = false,
        hasMore = true,
        retryOnError = true,
        retryDelay = 3000
    } = options

    const [isFetching, setIsFetching] = useState(false)
    const [error, setError] = useState<Error | null>(null)
    const loaderRef = useRef<HTMLDivElement | null>(null)
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const observerRef = useRef<IntersectionObserver | null>(null)

    // 清理重试定时器
    const clearRetryTimeout = useCallback(() => {
        if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current)
            retryTimeoutRef.current = null
        }
    }, [])

    // 加载更多内容的处理函数
    const loadMoreItems = useCallback(async () => {
        if (!hasMore || isLoading || isFetching) return

        setIsFetching(true)
        setError(null)
        clearRetryTimeout()

        try {
            await onLoadMore()
        } catch (err) {
            console.error("Failed to load more items:", err)
            setError(err instanceof Error ? err : new Error("Failed to load more items"))

            // 如果启用了自动重试，设置重试定时器
            if (retryOnError && hasMore) {
                retryTimeoutRef.current = setTimeout(() => {
                    setIsFetching(false)
                    // 只有当还有更多内容时才重试
                    if (hasMore) {
                        loadMoreItems()
                    }
                }, retryDelay)
            }
        } finally {
            setIsFetching(false)
        }
    }, [hasMore, isLoading, isFetching, onLoadMore, retryOnError, retryDelay, clearRetryTimeout])

    // 设置Intersection Observer
    useEffect(() => {
        // 如果没有更多内容，清理并返回
        if (!hasMore) {
            if (observerRef.current) {
                observerRef.current.disconnect()
                observerRef.current = null
            }
            return
        }

        // 清理之前的observer
        if (observerRef.current) {
            observerRef.current.disconnect()
        }

        // 创建新的observer
        observerRef.current = new IntersectionObserver(
            (entries) => {
                const [entry] = entries
                if (entry.isIntersecting && !isLoading && !isFetching && hasMore) {
                    loadMoreItems()
                }
            },
            {
                rootMargin,
                threshold: 0.1 // 当10%的元素可见时触发
            }
        )

        // 开始观察loader元素
        if (loaderRef.current && observerRef.current) {
            observerRef.current.observe(loaderRef.current)
        }

        // 清理函数
        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect()
                observerRef.current = null
            }
            clearRetryTimeout()
        }
    }, [hasMore, isLoading, isFetching, loadMoreItems, rootMargin, clearRetryTimeout])

    // 手动触发加载更多
    const triggerLoadMore = useCallback(() => {
        if (!isLoading && !isFetching && hasMore) {
            loadMoreItems()
        }
    }, [isLoading, isFetching, hasMore, loadMoreItems])

    return {
        loaderRef,
        isFetching,
        error,
        triggerLoadMore
    }
}
